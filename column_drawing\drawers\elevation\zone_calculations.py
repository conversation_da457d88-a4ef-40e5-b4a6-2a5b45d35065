"""
Zone Calculation Mixin
======================

Zone-related calculations and data handling for elevation diagrams.
"""

import logging
import math
from typing import Dict, Any, Optional
from ...models.column_config import ColumnConfig
from ...managers.link_mark_manager import get_global_link_mark_manager

logger = logging.getLogger(__name__)


class ZoneCalculationMixin:
    """Mixin class providing zone calculation functionality for elevation diagrams."""

    def _calculate_zone_link_info(self, zone_id: str, lap_length_mm: float, 
                                 column_config: ColumnConfig) -> Dict[str, Any]:
        """
        Calculate zone-specific link information including zone text and link descriptions.
        Stores results in cache for later recall and consistency checking.
        
        Args:
            zone_id: Zone identifier ('A', 'B', 'C', or 'D')
            lap_length_mm: Lap length in millimeters for link count calculation
            column_config: Column configuration data
            
        Returns:
            Dict containing zone text, link description and calculation details
        """
        try:
            # Import the zone display name utility function for consistency with section drawer
            from ...models.zone_config import get_zone_display_name

            # Determine the start floor type from column configuration
            start_floor_type = ""
            if hasattr(column_config, 'start_floor_name') and column_config.start_floor_name:
                start_floor_type = column_config.start_floor_name
            elif hasattr(column_config, 'floor') and column_config.floor:
                start_floor_type = column_config.floor

            # Get proper zone display name (consistent with section drawer)
            zone_text = get_zone_display_name(zone_id, start_floor_type)

            # Get zone-specific link data based on zone_id from CSV structure
            zone_data = self._get_zone_data_from_config(zone_id, column_config)
            
            # Store zone data in cache for later recall
            cache_key = f"{column_config.name}_{zone_id}"
            self.zone_data_cache[cache_key] = zone_data.copy()
            
            # Calculate main link count based on actual zone length, not lap length
            spacing = zone_data['spacing']

            # Calculate effective zone length for link placement
            # This should exclude spacer lengths and use actual zone dimensions
            effective_zone_length = self._calculate_effective_zone_length(zone_id, column_config)

            # Calculate link count: effective_length / spacing (rounded up to ensure coverage)
            if effective_zone_length > 0 and spacing > 0:
                # To ensure actual spacing is <= specified spacing, the number of links should be
                # calculated from the number of intervals required.
                # Number of intervals = ceil(length / spacing).
                # Number of links = Number of intervals + 1.
                num_intervals = math.ceil(effective_zone_length / spacing)
                main_link_count = int(num_intervals + 1)
                logger.info(f"=== DEBUG: Link count calculation for {column_config.name} Zone {zone_id} ===")
                logger.info(f"effective_zone_length={effective_zone_length}, spacing={spacing}")
                logger.info(f"effective_zone_length / spacing = {effective_zone_length / spacing}")
                logger.info(f"num_intervals = ceil({effective_zone_length / spacing}) = {num_intervals}")
                logger.info(f"main_link_count = {num_intervals} + 1 = {main_link_count}")
            else:
                main_link_count = 0  # No links if zone has no length or spacing is undefined
                logger.warning(f"=== DEBUG: Link count is 0 for {column_config.name} Zone {zone_id} ===")
                logger.warning(f"effective_zone_length={effective_zone_length}, spacing={spacing}")

            logger.debug(f"Zone {zone_id} link calculation: effective_length={effective_zone_length}mm, spacing={spacing}mm, count={main_link_count}")
            
            # Get link mark from global manager
            link_mark_manager = get_global_link_mark_manager()
            
            # Calculate 52 link length (stirrup perimeter)
            stirrup_width = getattr(column_config, 'width', 1000.0)
            stirrup_depth = getattr(column_config, 'depth', 1000.0)
            stirrup_perimeter = 2 * (stirrup_width + stirrup_depth)
            
            # Get pre-assigned mark for 52 link (lookup only, no new assignment)
            mark_52 = link_mark_manager.get_existing_mark(
                category="52",
                diameter=zone_data['outer_diameter'],
                column_mark=column_config.name
            )
            
            # For Zone A, combine with Zone C mark if they differ (after merging)
            if zone_id == 'A':
                mark_52_display = self._get_combined_link_mark(
                    mark_52.mark_number, 'C', '52', zone_data['outer_diameter'], column_config
                )
            else:
                mark_52_display = mark_52.mark_number
            
            # Main link description: "11T12-101-125"
            main_desc = f"{main_link_count}T{int(zone_data['outer_diameter'])}-{mark_52_display}-{int(spacing)}"
            
            # Check if 25a links are needed (intermediate legs) - use inner diameter
            has_25a_links = zone_data['legs_x'] > 2 or zone_data['legs_y'] > 2
            link_desc = main_desc
            mark_25a = None
            mark_25a_x = None
            mark_25a_y = None
            
            # Initialize display variables
            mark_25a_x_display = None
            mark_25a_y_display = None
            
            if has_25a_links and zone_data['inner_diameter'] > 0:
                # Calculate 25a link count: legs_x + legs_y - 4
                link_25a_count = zone_data['legs_x'] + zone_data['legs_y'] - 4

                # Calculate Y-direction link length for consistent marking (Y gets assigned first)
                # For elevation view, we need the column depth dimension for Y-direction links
                y_link_length = getattr(column_config, 'depth', 1000.0)  # Use column depth, fallback to 1000mm

                # Get pre-assigned mark for 25a Y-direction link (lookup only)
                mark_25a_y = link_mark_manager.get_existing_mark(
                    category="25A_Y",
                    diameter=zone_data['inner_diameter'],
                    column_mark=column_config.name,
                    length=y_link_length
                )

                # Calculate X-direction link length for consistent marking
                # For elevation view, we need the column width dimension for X-direction links
                x_link_length = getattr(column_config, 'width', 1000.0)  # Use column width, fallback to 1000mm

                # Get pre-assigned mark for 25a X-direction link (lookup only)
                mark_25a_x = link_mark_manager.get_existing_mark(
                    category="25A_X",
                    diameter=zone_data['inner_diameter'],
                    column_mark=column_config.name,
                    length=x_link_length
                )

                # If X/Y links are identical, they might share a mark.
                # If one is not found, try using the other's mark if lengths match.
                if mark_25a_x is None and mark_25a_y is not None and x_link_length == y_link_length:
                    logger.debug(f"Mark for 25A_X not found. Using 25A_Y mark ({mark_25a_y.mark_number}) due to identical length.")
                    mark_25a_x = mark_25a_y
                
                if mark_25a_y is None and mark_25a_x is not None and y_link_length == x_link_length:
                    logger.debug(f"Mark for 25A_Y not found. Using 25A_X mark ({mark_25a_x.mark_number}) due to identical length.")
                    mark_25a_y = mark_25a_x

                # For Zone A, combine with Zone C marks if they differ (after merging)
                if zone_id == 'A':
                    mark_25a_x_display = self._get_combined_link_mark(
                        mark_25a_x.mark_number, 'C', '25A_X', zone_data['inner_diameter'], column_config, x_link_length
                    )
                    mark_25a_y_display = self._get_combined_link_mark(
                        mark_25a_y.mark_number, 'C', '25A_Y', zone_data['inner_diameter'], column_config, y_link_length
                    )
                else:
                    mark_25a_x_display = mark_25a_x.mark_number
                    mark_25a_y_display = mark_25a_y.mark_number

                # Check if X and Y marks are different for special formatting
                if mark_25a_x_display != mark_25a_y_display:
                    logger.info(f"Zone {zone_id} has different 25A marks: Y={mark_25a_y_display}, X={mark_25a_x_display}")

                # 25a link description: "11x6T10-102-125" (main_count x intermediate_legs_count)
                # Use Y-direction mark for now (will be reformatted in dimension drawing if needed)
                if mark_25a_x_display == mark_25a_y_display:
                    link_25a_desc = f"{main_link_count}x{link_25a_count}T{int(zone_data['inner_diameter'])}-{mark_25a_y_display}-{int(spacing)}"
                else:
                    # This case will be handled by the reformatting logic in dimension_drawing
                    link_25a_desc = f"{main_link_count}x{link_25a_count}T{int(zone_data['inner_diameter'])}-{mark_25a_y_display}-{int(spacing)}"

                link_desc = f"{main_desc} & {link_25a_desc}"

                # Store both marks for later use in dimension formatting
                mark_25a = mark_25a_y  # Keep backward compatibility
            
            # Store link marks for later recall and consistency checking
            if column_config.name not in self.column_zone_marks:
                self.column_zone_marks[column_config.name] = {}

            # Store individual marks for all zones (no longer combine at this stage)
            stored_mark_52 = mark_52.mark_number
            stored_mark_25a_x = mark_25a_x.mark_number if has_25a_links and mark_25a_x else None
            stored_mark_25a_y = mark_25a_y.mark_number if has_25a_links and mark_25a_y else None

            # For Zone A, also store the combined display marks for backward compatibility
            if zone_id == 'A':
                stored_mark_52_display = mark_52_display
                stored_mark_25a_x_display = mark_25a_x_display if has_25a_links and mark_25a_x_display else None
                stored_mark_25a_y_display = mark_25a_y_display if has_25a_links and mark_25a_y_display else None

            self.column_zone_marks[column_config.name][zone_id] = {
                'mark_52': stored_mark_52,
                'mark_25a': mark_25a.mark_number if mark_25a else None,
                'mark_25a_x': stored_mark_25a_x,
                'mark_25a_y': stored_mark_25a_y,
                'outer_diameter': zone_data['outer_diameter'],
                'inner_diameter': zone_data['inner_diameter'],
                'floor_level': column_config.start_floor_name
            }

            # Store link marks in DataFrame for centralized management
            # Store individual zone data in zone-specific columns
            logger.info(f"=== ZONE {zone_id} PROCESSING === Storing individual zone marks for {column_config.name}")
            logger.info(f"Zone {zone_id} marks: outer={stored_mark_52}, inner_x={stored_mark_25a_x}, inner_y={stored_mark_25a_y}")
            self._store_individual_zone_marks_in_dataframe(
                column_config, zone_id, main_link_count, stored_mark_52,
                link_25a_count if has_25a_links else 0, stored_mark_25a_x, stored_mark_25a_y
            )

            # For Zone A, also handle the combined A+C marks using the new DataFrame zone merging
            if zone_id == 'A':
                logger.info(f"=== ZONE A PROCESSING === Handling Zone A+C merging using DataFrame zone merging method")
                # We need to get Zone C marks to properly merge them
                self._handle_zone_a_c_merging(
                    column_config, main_link_count, stored_mark_52,
                    link_25a_count if has_25a_links else 0, stored_mark_25a_x, stored_mark_25a_y
                )
            
            # Store complete calculated info for consistency checks
            calculated_info = {
                'zone_id': zone_id,
                'zone_text': zone_text,  # Now uses proper zone display name
                'link_description': link_desc,
                'main_count': main_link_count,
                'spacing': spacing,
                'has_25a': has_25a_links,
                'zone_data': zone_data,
                'marks': {
                    'mark_52': stored_mark_52,
                    'mark_25a': mark_25a.mark_number if mark_25a else None,
                    'mark_25a_x': stored_mark_25a_x,
                    'mark_25a_y': stored_mark_25a_y
                }
            }
            
            self.calculated_zone_info[cache_key] = calculated_info
            
            return calculated_info
            
        except Exception as e:
            logger.error(f"Error calculating zone link info for {zone_id}: {e}")
            # Use proper zone display name even in error case
            try:
                from ...models.zone_config import get_zone_display_name
                start_floor_type = getattr(column_config, 'start_floor_name', '')
                zone_text = get_zone_display_name(zone_id, start_floor_type)
            except:
                zone_text = f"ZONE {zone_id}"
            
            error_info = {
                'zone_id': zone_id,
                'zone_text': zone_text,
                'link_description': "ERROR",
                'main_count': 0,
                'spacing': 125,
                'has_25a': False,
                'zone_data': {},
                'marks': {'mark_52': 'ERROR', 'mark_25a': None}
            }
            
            # Store error info for debugging
            cache_key = f"{column_config.name}_{zone_id}"
            self.calculated_zone_info[cache_key] = error_info
            
            return error_info

    def _calculate_physical_zone_lengths(self, column_config: ColumnConfig) -> Dict[str, float]:
        """
        Calculates the physical lengths of reinforcement zones A, B, C, and D based on engineering rules.
        """
        logger.info(f"=== DEBUG: Calculating physical zone lengths for {column_config.name} ===")
        
        end_floor_y = None
        start_floor_y = None
        H = 3500.0 # Default fallback
        
        # Debug: Log all available attributes
        logger.info(f"Available column_config attributes: {[attr for attr in dir(column_config) if not attr.startswith('_')]}")
        
        try:
            # The CSV data is stored in these attributes from csv_reader.py:
            # - start_floor_level (from 'Start Floor Level (mPD)')
            # - end_floor_level (from 'End Floor Level (mPD)')
            # - lowest_beam_soffit (from 'Lowest Beam Soffit (mPD)')
            
            start_floor_y = column_config.start_floor_level
            end_floor_y = column_config.end_floor_level
            
            logger.info(f"Raw CSV values: start_floor_level={start_floor_y}, end_floor_level={end_floor_y}")
            
            if start_floor_y != 0.0 and end_floor_y != 0.0:
                H = abs(end_floor_y - start_floor_y)
                logger.info(f"Calculated from CSV data: start_floor_y={start_floor_y}, end_floor_y={end_floor_y}, H={H}")
            else:
                logger.warning(f"CSV floor levels are 0.0 (missing data): start={start_floor_y}, end={end_floor_y}")
                raise ValueError("Floor level data missing from CSV")
                
        except (ValueError, TypeError, AttributeError) as e:
            logger.warning(f"Could not get levels from CSV data for {column_config.name}: {e}. Falling back to floor name lookup.")
            try:
                start_floor_y = self.config.get_floor_y(column_config.start_floor_name)
                end_floor_y = self.config.get_floor_y(column_config.end_floor_name)
                H = abs(end_floor_y - start_floor_y)
                logger.info(f"Got levels from floor name lookup: start_floor_y={start_floor_y}, end_floor_y={end_floor_y}, H={H}")
            except (KeyError, AttributeError) as e_fallback:
                 logger.warning(f"Floor name lookup failed for {column_config.name}: {e_fallback}. Using default H=3500mm.")

        # L_c: Top confinement zone length
        column_width = getattr(column_config, 'B', 0)  # Use B instead of width
        column_depth = getattr(column_config, 'D', 0)  # Use D instead of depth
        # Convert mm to meters for comparison
        column_width_m = column_width / 1000.0 if column_width > 0 else 0
        column_depth_m = column_depth / 1000.0 if column_depth > 0 else 0
        L_c = max(H / 6, column_width_m, column_depth_m, 0.5)  # 500mm = 0.5m
        logger.info(f"L_c calculation: H/6={H/6:.3f}m, width={column_width}mm ({column_width_m:.3f}m), depth={column_depth}mm ({column_depth_m:.3f}m), L_c={L_c:.3f}m")

        # L_a: Bottom confinement zone length
        lap_length_mm = self._calculate_lap_length_for_zone(column_config)  # Returns mm
        lap_length_m = lap_length_mm / 1000.0  # Convert to meters for comparison
        L_a = max(lap_length_m, L_c)
        logger.info(f"L_a calculation: lap_length={lap_length_mm}mm ({lap_length_m:.3f}m), L_c={L_c:.3f}m, L_a={L_a:.3f}m")
        
        # L_b: Central zone length
        if (L_a + L_c) >= H:
            logger.warning(f"Confinement zones A ({L_a:.3f}m) and C ({L_c:.3f}m) overlap or exceed floor height ({H:.3f}m). Adjusting lengths.")
            L_a = H / 2
            L_c = H / 2
            L_b = 0
        else:
            L_b = H - L_a - L_c
        logger.info(f"L_b calculation: H={H:.3f}m, L_a={L_a:.3f}m, L_c={L_c:.3f}m, L_b={L_b:.3f}m")

        # L_d: Anchorage zone length into floor above.
        # Per user spec: "end floor level - lowest beam soffit level".
        L_d = 400.0  # Default fallback
        logger.info(f"L_d calculation debug: end_floor_y={end_floor_y}")
        
        if end_floor_y is not None:
            # Debug: Check for the CSV attribute name
            lowest_beam_soffit_level = column_config.lowest_beam_soffit
            logger.info(f"lowest_beam_soffit_level from CSV: {lowest_beam_soffit_level}")
            
            if lowest_beam_soffit_level != 0.0:
                try:
                    L_d = abs(end_floor_y - lowest_beam_soffit_level)
                    logger.info(f"L_d calculated: end_floor_y={end_floor_y}, soffit_y={lowest_beam_soffit_level}, L_d={L_d}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid 'lowest_beam_soffit' value for {column_config.name}: {lowest_beam_soffit_level}. Using fallback for L_d.")
            else:
                logger.warning(f"'lowest_beam_soffit' is 0.0 (missing from CSV) for {column_config.name}. Using fallback for L_d.")
        else:
            logger.warning("end_floor_y not available, cannot calculate L_d from soffit level. Using fallback.")

        # Convert from meters to millimeters (CSV floor levels are in meters)
        L_a_mm = L_a * 1000
        L_b_mm = L_b * 1000
        L_c_mm = L_c * 1000
        L_d_mm = L_d * 1000
        
        lengths = {'A': L_a_mm, 'B': L_b_mm, 'C': L_c_mm, 'D': L_d_mm}
        logger.info(f"Physical zone lengths (meters): A={L_a:.3f}, B={L_b:.3f}, C={L_c:.3f}, D={L_d:.3f}")
        logger.info(f"Final calculated physical zone lengths (mm) for {column_config.name}: {lengths}")
        return lengths

    def _calculate_effective_zone_length(self, zone_id: str, column_config: ColumnConfig) -> float:
        """
        Calculate the effective zone length available for link placement, accounting for link spacing at zone boundaries.
        Caches physical lengths to avoid redundant calculations.
        """
        logger.info(f"=== DEBUG: Calculating effective zone length for {column_config.name} Zone {zone_id} ===")
        
        if not hasattr(self, 'physical_zone_lengths_cache'):
            self.physical_zone_lengths_cache = {}

        cache_key = column_config.name
        if cache_key not in self.physical_zone_lengths_cache:
            self.physical_zone_lengths_cache[cache_key] = self._calculate_physical_zone_lengths(column_config)
        
        physical_lengths = self.physical_zone_lengths_cache[cache_key]
        physical_length = physical_lengths.get(zone_id, 0.0)
        logger.info(f"Physical length for Zone {zone_id}: {physical_length}")

        if physical_length <= 0:
            logger.warning(f"Physical length is {physical_length} for Zone {zone_id}, returning 0")
            return 0.0

        # Get zone-specific link spacing
        zone_data = self._get_zone_data_from_config(zone_id, column_config)
        spacing = zone_data.get('spacing', 125.0)
        logger.info(f"Zone {zone_id} data: {zone_data}")
        logger.info(f"Spacing for Zone {zone_id}: {spacing}")

        effective_length = physical_length
        if zone_id == 'B':
            effective_length -= spacing
        elif zone_id == 'C':
            effective_length -= spacing
        elif zone_id == 'D':
            effective_length -= 2 * spacing
        # For Zone A, effective_length is the same as physical_length

        # Ensure effective length is not negative
        final_length = max(0.0, effective_length)
        
        logger.info(f"Zone {zone_id}: physical_length={physical_length:.1f}, spacing={spacing}, effective_length_before_max={effective_length:.1f}, final_length={final_length:.1f}")
        
        return final_length

    def _calculate_lap_length_for_zone(self, column_config: ColumnConfig) -> float:
        """
        Calculate lap length for the zone based on rebar diameter and concrete grade.

        Args:
            column_config: Column configuration data

        Returns:
            float: Lap length in millimeters
        """
        # Import rebar calculator
        from ...calculators.rebar_calculator import RebarCalculator

        rebar_calc = RebarCalculator()
        lap_length = rebar_calc.lap_length(column_config.dia1, column_config.concrete_grade)

        logger.debug(f"Calculated lap length: {lap_length}mm for {column_config.dia1}mm rebar in {column_config.concrete_grade} concrete")
        return lap_length

    def _get_zone_data_from_config(self, zone_id: str, column_config: ColumnConfig) -> Dict:
        """
        Get zone-specific data from column configuration based on CSV structure.
        Enhanced to use the new ZoneConfigSet system for proper zone data handling.

        Args:
            zone_id: Zone identifier ('A', 'B', 'C', or 'D')
            column_config: Column configuration with zone data

        Returns:
            Dict containing zone-specific link data
        """
        # Enhanced default fallback with better inner diameter handling
        default_data = {
            'outer_diameter': getattr(column_config, 'dia_links', 12.0),
            'inner_diameter': getattr(column_config, 'dia_inner_links', 0.0),  # Check for inner diameter field
            'spacing': getattr(column_config, 'spacing_typical', 125.0),
            'legs_x': getattr(column_config, 'num_legs_x', 2),
            'legs_y': getattr(column_config, 'num_legs_y', 2)
        }

        # First, try to get zone data from the new ZoneConfigSet system
        # Use the zone_config_set passed to draw_elevation_diagrams, or check column_config
        zone_config_set = getattr(self, 'current_zone_config_set', None)
        if zone_config_set is None:
            if hasattr(column_config, 'zone_config_set'):
                zone_config_set = column_config.zone_config_set
            elif hasattr(column_config, 'get_zone_config_set'):
                zone_config_set = column_config.get_zone_config_set()

        if zone_config_set is not None:
            try:
                zone_config = zone_config_set.get_zone(zone_id)
                if zone_config is not None:
                    logger.debug(f"Using ZoneConfigSet for zone {zone_id}: outer={zone_config.outer_link_diameter}, inner={zone_config.inner_link_diameter}, spacing={zone_config.link_spacing}, legs=({zone_config.link_legs_x}x{zone_config.link_legs_y})")

                    return {
                        'outer_diameter': zone_config.outer_link_diameter,
                        'inner_diameter': zone_config.inner_link_diameter,
                        'spacing': zone_config.link_spacing,
                        'legs_x': zone_config.link_legs_x,
                        'legs_y': zone_config.link_legs_y
                    }
            except Exception as e:
                logger.warning(f"Failed to get zone config from ZoneConfigSet: {e}")

        logger.debug(f"ZoneConfigSet not available for zone {zone_id}, falling back to individual attributes")

        # Try to get zone-specific data from enhanced column config
        # This will be available when CSV has full zone data
        zone_attr_map = {
            'A': {
                'outer_diameter': 'zone_a_outer_diameter',
                'inner_diameter': 'zone_a_inner_diameter',
                'spacing': 'zone_a_spacing',
                'legs_x': 'zone_a_legs_x',
                'legs_y': 'zone_a_legs_y'
            },
            'B': {
                'outer_diameter': 'zone_b_outer_diameter',
                'inner_diameter': 'zone_b_inner_diameter',
                'spacing': 'zone_b_spacing',
                'legs_x': 'zone_b_legs_x',
                'legs_y': 'zone_b_legs_y'
            },
            'C': {
                'outer_diameter': 'zone_c_outer_diameter',
                'inner_diameter': 'zone_c_inner_diameter',
                'spacing': 'zone_c_spacing',
                'legs_x': 'zone_c_legs_x',
                'legs_y': 'zone_c_legs_y'
            },
            'D': {
                'outer_diameter': 'zone_d_outer_diameter',
                'inner_diameter': 'zone_d_inner_diameter',
                'spacing': 'zone_d_spacing',
                'legs_x': 'zone_d_legs_x',
                'legs_y': 'zone_d_legs_y'
            }
        }

        if zone_id in zone_attr_map:
            zone_attrs = zone_attr_map[zone_id]
            zone_data = {}

            # Try to get each attribute from the column config
            for data_key, attr_name in zone_attrs.items():
                if hasattr(column_config, attr_name):
                    value = getattr(column_config, attr_name)

                    # Ensure numeric values are properly converted
                    if data_key in ['outer_diameter', 'inner_diameter', 'spacing']:
                        try:
                            zone_data[data_key] = float(value) if value is not None else default_data[data_key]
                        except (ValueError, TypeError):
                            logger.warning(f"Invalid {data_key} value for zone {zone_id}: {value}, using default")
                            zone_data[data_key] = default_data[data_key]
                    elif data_key in ['legs_x', 'legs_y']:
                        try:
                            zone_data[data_key] = int(value) if value is not None else default_data[data_key]
                        except (ValueError, TypeError):
                            logger.warning(f"Invalid {data_key} value for zone {zone_id}: {value}, using default")
                            zone_data[data_key] = default_data[data_key]
                    else:
                        zone_data[data_key] = value
                else:
                    # Fallback to default data
                    logger.warning(f"Missing attribute {attr_name} for zone {zone_id}, using default {default_data[data_key]}")
                    zone_data[data_key] = default_data[data_key]

            # Additional consistency checks
            if zone_data['inner_diameter'] > zone_data['outer_diameter'] and zone_data['inner_diameter'] > 0:
                logger.warning(f"Zone {zone_id}: Inner diameter ({zone_data['inner_diameter']}) > Outer diameter ({zone_data['outer_diameter']}), setting inner to 0")
                zone_data['inner_diameter'] = 0.0

            if zone_data['spacing'] <= 0:
                logger.warning(f"Zone {zone_id}: Invalid spacing ({zone_data['spacing']}), using default")
                zone_data['spacing'] = default_data['spacing']

            # Log zone data for debugging
            logger.debug(f"Zone {zone_id} data: outer={zone_data['outer_diameter']}, inner={zone_data['inner_diameter']}, spacing={zone_data['spacing']}, legs=({zone_data['legs_x']}x{zone_data['legs_y']})")

            return zone_data

        # Fallback for unknown zone
        logger.warning(f"Unknown zone ID: {zone_id}, using default data")
        return default_data

    def get_cached_zone_data(self, column_name: str, zone_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve cached zone data for consistency checking and debugging.

        Args:
            column_name: Column identifier
            zone_id: Zone identifier ('A', 'B', 'C', or 'D')

        Returns:
            Cached zone data if available, None otherwise
        """
        cache_key = f"{column_name}_{zone_id}"
        return self.calculated_zone_info.get(cache_key)

    def _store_combined_zone_marks_in_dataframe(self, column_config: ColumnConfig,
                                              outer_count: int, combined_outer_mark: str,
                                              inner_count: int, combined_inner_x_mark: str,
                                              combined_inner_y_mark: str) -> None:
        """
        Store combined Zone A+C link marks in the DataFrame for centralized management.

        Args:
            column_config: Column configuration
            outer_count: Number of outer links
            combined_outer_mark: Combined outer link mark (already includes A+C merging)
            inner_count: Number of inner links
            combined_inner_x_mark: Combined inner X link mark (already includes A+C merging)
            combined_inner_y_mark: Combined inner Y link mark (already includes A+C merging)
        """
        try:
            logger.info(f"=== DATAFRAME STORAGE === Attempting to store combined marks for {column_config.name}")
            logger.info(f"Column attributes: name={column_config.name}, start_floor_name={getattr(column_config, 'start_floor_name', 'NOT_FOUND')}")
            logger.info(f"Marks to store: outer_count={outer_count}, outer_mark={combined_outer_mark}")
            logger.info(f"Inner marks: inner_x_count={inner_count}, inner_x_mark={combined_inner_x_mark}, inner_y_count={inner_count}, inner_y_mark={combined_inner_y_mark}")

            # Get DataFrame manager
            if hasattr(self, 'dataframe_manager'):
                logger.info(f"DataFrame manager found: {self.dataframe_manager}")
                if self.dataframe_manager.is_loaded():
                    logger.info("DataFrame is loaded, proceeding with update")
                    # Store the combined marks for Zone A (which includes Zone C data)
                    success = self.dataframe_manager.update_link_marks(
                        column_mark=column_config.name,
                        start_floor=column_config.start_floor_name,
                        outer_count=outer_count,
                        outer_mark=combined_outer_mark,
                        inner_x_count=inner_count,
                        inner_x_mark=combined_inner_x_mark,
                        inner_y_count=inner_count,
                        inner_y_mark=combined_inner_y_mark
                    )
                    if success:
                        logger.info(f"Successfully stored combined Zone A+C link marks in DataFrame for {column_config.name}")
                    else:
                        logger.error(f"Failed to store combined Zone A+C link marks in DataFrame for {column_config.name}")
                else:
                    logger.warning("DataFrame manager exists but DataFrame is not loaded")
            else:
                logger.warning("DataFrame manager not available")

        except Exception as e:
            logger.error(f"Error storing combined link marks in DataFrame for {column_config.name}: {e}")

    def _store_individual_zone_marks_in_dataframe(self, column_config: ColumnConfig, zone_id: str,
                                                outer_count: int, outer_mark: str,
                                                inner_count: int, inner_x_mark: str,
                                                inner_y_mark: str) -> None:
        """
        Store individual zone link marks in the DataFrame for centralized management.

        Args:
            column_config: Column configuration
            zone_id: Zone identifier (A, B, C, or D)
            outer_count: Number of outer links
            outer_mark: Outer link mark for this zone
            inner_count: Number of inner links
            inner_x_mark: Inner X link mark for this zone
            inner_y_mark: Inner Y link mark for this zone
        """
        try:
            logger.info(f"=== INDIVIDUAL ZONE DATAFRAME STORAGE === Storing marks for {column_config.name}, zone {zone_id}")
            logger.info(f"Zone {zone_id} marks: outer_count={outer_count}, outer_mark={outer_mark}")
            logger.info(f"Zone {zone_id} inner marks: inner_count={inner_count}, inner_x_mark={inner_x_mark}, inner_y_mark={inner_y_mark}")

            # Get DataFrame manager
            if hasattr(self, 'dataframe_manager'):
                logger.info(f"DataFrame manager found: {self.dataframe_manager}")
                if self.dataframe_manager.is_loaded():
                    logger.info("DataFrame is loaded, proceeding with zone-specific update")
                    # Store the individual zone marks in zone-specific columns
                    success = self.dataframe_manager.update_link_marks_for_zone(
                        column_mark=column_config.name,
                        start_floor=column_config.start_floor_name,
                        zone_id=zone_id,
                        outer_count=outer_count,
                        outer_mark=outer_mark,
                        inner_x_count=inner_count,
                        inner_x_mark=inner_x_mark,
                        inner_y_count=inner_count,
                        inner_y_mark=inner_y_mark
                    )

                    if success:
                        logger.info(f"Successfully stored individual zone marks for {column_config.name}, zone {zone_id}")
                    else:
                        logger.error(f"Failed to store individual zone marks for {column_config.name}, zone {zone_id}")
                else:
                    logger.warning("DataFrame not loaded, cannot store individual zone marks")
            else:
                logger.warning("DataFrame manager not available, cannot store individual zone marks")

        except Exception as e:
            logger.error(f"Error storing individual zone marks in DataFrame: {e}")

    def _handle_zone_a_c_merging(self, column_config: ColumnConfig,
                                outer_count: int, zone_a_outer_mark: str,
                                inner_count: int, zone_a_inner_x_mark: str,
                                zone_a_inner_y_mark: str) -> None:
        """
        Handle Zone A+C merging using the new DataFrame zone merging method.

        This method gets Zone C marks and uses the DataFrame's store_zone_marks_for_merging
        method to properly store both individual zone data and combined data.

        Args:
            column_config: Column configuration
            outer_count: Number of outer links
            zone_a_outer_mark: Zone A outer link mark
            inner_count: Number of inner links
            zone_a_inner_x_mark: Zone A inner X link mark
            zone_a_inner_y_mark: Zone A inner Y link mark
        """
        try:
            logger.info(f"=== ZONE A+C MERGING === Handling Zone A+C merging for {column_config.name}")

            # Get Zone C marks from the link mark manager
            from ...managers.link_mark_manager import get_global_link_mark_manager
            link_mark_manager = get_global_link_mark_manager()

            # Get Zone C outer mark (52 link)
            zone_c_outer_mark_data = link_mark_manager.get_existing_mark(
                category="52",
                diameter=self._get_zone_data_from_config('C', column_config)['outer_diameter'],
                column_mark=column_config.name
            )
            zone_c_outer_mark = zone_c_outer_mark_data.mark_number if zone_c_outer_mark_data else zone_a_outer_mark

            # Get Zone C inner marks (25A links) if they exist
            zone_c_inner_x_mark = zone_a_inner_x_mark  # Default to Zone A mark
            zone_c_inner_y_mark = zone_a_inner_y_mark  # Default to Zone A mark

            if zone_a_inner_x_mark:
                zone_c_inner_x_mark_data = link_mark_manager.get_existing_mark(
                    category="25A_X",
                    diameter=self._get_zone_data_from_config('C', column_config)['inner_diameter'],
                    column_mark=column_config.name
                )
                if zone_c_inner_x_mark_data:
                    zone_c_inner_x_mark = zone_c_inner_x_mark_data.mark_number

            if zone_a_inner_y_mark:
                zone_c_inner_y_mark_data = link_mark_manager.get_existing_mark(
                    category="25A_Y",
                    diameter=self._get_zone_data_from_config('C', column_config)['inner_diameter'],
                    column_mark=column_config.name
                )
                if zone_c_inner_y_mark_data:
                    zone_c_inner_y_mark = zone_c_inner_y_mark_data.mark_number

            logger.info(f"Zone A marks: outer={zone_a_outer_mark}, inner_x={zone_a_inner_x_mark}, inner_y={zone_a_inner_y_mark}")
            logger.info(f"Zone C marks: outer={zone_c_outer_mark}, inner_x={zone_c_inner_x_mark}, inner_y={zone_c_inner_y_mark}")

            # Use DataFrame's zone merging method
            if hasattr(self, 'dataframe_manager') and self.dataframe_manager.is_loaded():
                success = self.dataframe_manager.store_zone_marks_for_merging(
                    column_mark=column_config.name,
                    start_floor=column_config.start_floor_name,
                    zone_a_outer_mark=zone_a_outer_mark,
                    zone_c_outer_mark=zone_c_outer_mark,
                    zone_a_inner_x_mark=zone_a_inner_x_mark,
                    zone_c_inner_x_mark=zone_c_inner_x_mark,
                    zone_a_inner_y_mark=zone_a_inner_y_mark,
                    zone_c_inner_y_mark=zone_c_inner_y_mark,
                    outer_count=outer_count,
                    inner_x_count=inner_count,
                    inner_y_count=inner_count
                )

                if success:
                    logger.info(f"Successfully stored Zone A+C merged marks for {column_config.name}")
                else:
                    logger.error(f"Failed to store Zone A+C merged marks for {column_config.name}")
            else:
                logger.warning("DataFrame manager not available for Zone A+C merging")

        except Exception as e:
            logger.error(f"Error handling Zone A+C merging: {e}")

    def get_column_zone_marks(self, column_name: str) -> Optional[Dict[str, Dict[str, str]]]:
        """
        Retrieve all zone marks for a column for consistency checking.

        Args:
            column_name: Column identifier

        Returns:
            Dictionary of zone marks if available, None otherwise
        """
        return self.column_zone_marks.get(column_name)

    def validate_zone_consistency(self, column_config: ColumnConfig) -> Dict[str, Any]:
        """
        Validate consistency of zone data across all zones for a column.
        Useful for debugging and ensuring data integrity.

        Args:
            column_config: Column configuration to validate

        Returns:
            Validation report with consistency findings
        """
        report = {
            'column_name': column_config.name,
            'zones_processed': [],
            'consistency_issues': [],
            'diameter_summary': {},
            'spacing_summary': {},
            'marks_summary': {}
        }

        try:
            for zone_id in ['A', 'B', 'C', 'D']:
                cache_key = f"{column_config.name}_{zone_id}"
                zone_info = self.calculated_zone_info.get(cache_key)

                if zone_info:
                    report['zones_processed'].append(zone_id)

                    # Track diameter consistency
                    zone_data = zone_info.get('zone_data', {})
                    outer_dia = zone_data.get('outer_diameter', 0)
                    inner_dia = zone_data.get('inner_diameter', 0)
                    spacing = zone_data.get('spacing', 0)

                    report['diameter_summary'][zone_id] = {
                        'outer': outer_dia,
                        'inner': inner_dia
                    }
                    report['spacing_summary'][zone_id] = spacing

                    # Track mark consistency
                    marks = zone_info.get('marks', {})
                    report['marks_summary'][zone_id] = marks

                    # Check for potential issues
                    if inner_dia > outer_dia and inner_dia > 0:
                        report['consistency_issues'].append(
                            f"Zone {zone_id}: Inner diameter ({inner_dia}) > Outer diameter ({outer_dia})"
                        )

                    if spacing <= 0:
                        report['consistency_issues'].append(
                            f"Zone {zone_id}: Invalid spacing ({spacing})"
                        )

            # Cross-zone consistency checks
            outer_diameters = [info['outer'] for info in report['diameter_summary'].values()]
            if len(set(outer_diameters)) > 2:  # Allow some variation but flag excessive differences
                report['consistency_issues'].append(
                    f"High variation in outer diameters across zones: {outer_diameters}"
                )

            spacings = [spacing for spacing in report['spacing_summary'].values()]
            if len(set(spacings)) > 2:  # Allow some variation but flag excessive differences
                report['consistency_issues'].append(
                    f"High variation in spacing across zones: {spacings}"
                )

        except Exception as e:
            report['consistency_issues'].append(f"Validation error: {e}")
            logger.error(f"Error validating zone consistency: {e}")

        return report

    def _get_combined_link_mark(self, zone_a_mark: str, zone_c_id: str, category: str, 
                               diameter: float, column_config: ColumnConfig, 
                               length: float = None) -> str:
        """
        Get combined link mark for Zone A that includes Zone C mark if different.
        
        Args:
            zone_a_mark: Zone A mark number
            zone_c_id: Zone C identifier ('C')
            category: Link category ('52', '25A_X', '25A_Y')
            diameter: Link diameter
            column_config: Column configuration
            length: Link length (for 25A links)
            
        Returns:
            Combined mark string (e.g., "101/103" if different, "101" if same)
        """
        try:
            # Get link mark manager
            from ...managers.link_mark_manager import get_global_link_mark_manager
            link_mark_manager = get_global_link_mark_manager()
            
            # Get Zone C mark for comparison
            if length is not None:
                zone_c_mark = link_mark_manager.get_existing_mark(
                    category=category,
                    diameter=diameter,
                    column_mark=column_config.name,
                    length=length
                )
            else:
                zone_c_mark = link_mark_manager.get_existing_mark(
                    category=category,
                    diameter=diameter,
                    column_mark=column_config.name
                )
            
            # Debug logging to understand mark combination behavior
            if zone_c_mark:
                logger.info(f"Zone C mark found for {category}: Zone A={zone_a_mark}, Zone C={zone_c_mark.mark_number}")
                if zone_c_mark.mark_number != zone_a_mark:
                    combined_mark = f"{zone_a_mark}/{zone_c_mark.mark_number}"
                    logger.info(f"Combined Zone A and C marks for {category}: {combined_mark}")
                    return combined_mark
                else:
                    logger.info(f"Zone A and C marks are identical for {category}: {zone_a_mark}")
                    return zone_a_mark
            else:
                logger.warning(f"Zone C mark not found for {category}, using Zone A mark: {zone_a_mark}")
                return zone_a_mark
                
        except Exception as e:
            logger.warning(f"Error getting Zone C mark for {category}: {e}")
            # Return Zone A mark if there's an error accessing Zone C
            return zone_a_mark
