#!/usr/bin/env python3
"""
Test script to verify the new horizontal zone-specific storage structure.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from column_drawing.managers.dataframe_manager import DataFrameManager

def test_horizontal_storage():
    """Test the new horizontal zone-specific storage structure."""
    print("=== Horizontal Zone-Specific Storage Test ===")
    
    # Create a test CSV file
    test_csv_data = {
        'Start Floor': ['PILE CAP', '1/F', 'PILE CAP'],
        'End Floor': ['1/F', '2/F', '1/F'],
        'Column Mark': ['A3', 'A3', 'D7'],
        'B (mm)': [550, 550, 700],
        'D (mm)': [650, 650, 700],
        'Cover (mm)': [40, 40, 40],
        'Layer 1 Rebar Diameter (mm)': [32, 32, 25],
        'Layer 1 Rebar Quantity X': [5, 5, 5],
        'Layer 1 Rebar Quantity Y': [5, 5, 5]
    }
    
    test_df = pd.DataFrame(test_csv_data)
    test_csv_filename = 'test_horizontal_storage.csv'
    test_df.to_csv(test_csv_filename, index=False)
    print(f"Created test CSV file: {test_csv_filename}")
    
    try:
        # Initialize DataFrame manager
        df_manager = DataFrameManager()
        df = df_manager.load_csv_data(test_csv_filename)
        
        print(f"\nDataFrame loaded with {len(df)} rows")
        print(f"Total columns: {len(df.columns)}")
        
        # Check that zone-specific columns were added
        zone_columns = [col for col in df.columns if col.startswith('Zone ')]
        print(f"Zone-specific columns added: {len(zone_columns)}")
        print("Sample zone columns:", zone_columns[:6])
        
        # Test storing individual zone data
        print("\n=== Testing Individual Zone Storage ===")
        
        # Store data for Zone A
        success_a = df_manager.update_link_marks_for_zone(
            column_mark='A3',
            start_floor='PILE CAP',
            zone_id='A',
            outer_count=4,
            outer_mark='(101)',
            inner_x_count=2,
            inner_x_mark='(102)',
            inner_y_count=2,
            inner_y_mark='(103)'
        )
        print(f"Zone A storage success: {success_a}")
        
        # Store data for Zone B
        success_b = df_manager.update_link_marks_for_zone(
            column_mark='A3',
            start_floor='PILE CAP',
            zone_id='B',
            outer_count=4,
            outer_mark='(104)',
            inner_x_count=2,
            inner_x_mark='(105)',
            inner_y_count=2,
            inner_y_mark='(106)'
        )
        print(f"Zone B storage success: {success_b}")
        
        # Store data for Zone C
        success_c = df_manager.update_link_marks_for_zone(
            column_mark='A3',
            start_floor='PILE CAP',
            zone_id='C',
            outer_count=4,
            outer_mark='(107)',
            inner_x_count=2,
            inner_x_mark='(108)',
            inner_y_count=2,
            inner_y_mark='(109)'
        )
        print(f"Zone C storage success: {success_c}")
        
        # Store data for Zone D
        success_d = df_manager.update_link_marks_for_zone(
            column_mark='A3',
            start_floor='PILE CAP',
            zone_id='D',
            outer_count=4,
            outer_mark='(110)',
            inner_x_count=2,
            inner_x_mark='(111)',
            inner_y_count=2,
            inner_y_mark='(112)'
        )
        print(f"Zone D storage success: {success_d}")
        
        # Test retrieving individual zone data
        print("\n=== Testing Individual Zone Retrieval ===")
        
        for zone_id in ['A', 'B', 'C', 'D']:
            zone_data = df_manager.get_link_marks_for_zone('A3', 'PILE CAP', zone_id)
            print(f"Zone {zone_id} data: {zone_data}")
        
        # Test zone merging functionality
        print("\n=== Testing Zone A+C Merging ===")
        
        merge_success = df_manager.store_zone_marks_for_merging(
            column_mark='D7',
            start_floor='PILE CAP',
            zone_a_outer_mark='(201)',
            zone_c_outer_mark='(204)',
            zone_a_inner_x_mark='(202)',
            zone_c_inner_x_mark='(205)',
            zone_a_inner_y_mark='(203)',
            zone_c_inner_y_mark='(206)',
            outer_count=4,
            inner_x_count=2,
            inner_y_count=2
        )
        print(f"Zone A+C merging success: {merge_success}")
        
        # Check individual zone data after merging
        zone_a_data = df_manager.get_link_marks_for_zone('D7', 'PILE CAP', 'A')
        zone_c_data = df_manager.get_link_marks_for_zone('D7', 'PILE CAP', 'C')
        print(f"Zone A individual data: {zone_a_data}")
        print(f"Zone C individual data: {zone_c_data}")
        
        # Check combined data (generic columns)
        combined_data = df_manager.get_link_marks('D7', 'PILE CAP')
        print(f"Combined data (generic columns): {combined_data}")
        
        # Display the final DataFrame structure
        print("\n=== Final DataFrame Structure ===")
        print(f"DataFrame shape: {df.shape}")
        
        # Show a sample of the data
        print("\nSample data for A3 PILE CAP:")
        a3_row = df[(df['Column Mark'] == 'A3') & (df['Start Floor'] == 'PILE CAP')]
        if not a3_row.empty:
            row_data = a3_row.iloc[0]
            print("Zone-specific columns:")
            for col in df.columns:
                if col.startswith('Zone '):
                    value = row_data[col]
                    if pd.notna(value) and value != '' and value != 0:
                        print(f"  {col}: {value}")
        
        # Test individual zone storage convenience method
        print("\n=== Testing Individual Zone Storage Convenience Method ===")
        individual_success = df_manager.store_individual_zone_marks(
            column_mark='A3',
            start_floor='1/F',
            zone_id='B',
            outer_count=6,
            outer_mark='(301)',
            inner_x_count=3,
            inner_x_mark='(302)',
            inner_y_count=3,
            inner_y_mark='(303)'
        )
        print(f"Individual zone storage (A3 1/F Zone B) success: {individual_success}")
        
        # Verify the storage
        zone_b_1f_data = df_manager.get_link_marks_for_zone('A3', '1/F', 'B')
        print(f"Zone B 1/F data: {zone_b_1f_data}")
        
        print("\n=== Test Complete ===")
        print("✓ Zone-specific columns added successfully")
        print("✓ Individual zone storage working")
        print("✓ Individual zone retrieval working")
        print("✓ Zone A+C merging working with both individual and combined storage")
        print("✓ Convenience methods working")
        print("✓ Horizontal storage structure implemented successfully")
        
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up test file
        if os.path.exists(test_csv_filename):
            os.remove(test_csv_filename)
            print(f"\nCleaned up test file: {test_csv_filename}")

if __name__ == "__main__":
    test_horizontal_storage()
