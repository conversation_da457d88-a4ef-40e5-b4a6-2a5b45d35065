#!/usr/bin/env python3
"""
Test script to verify the horizontal zone-specific storage integration with the full drawing generation process.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from column_drawing.managers.dataframe_manager import get_global_dataframe_manager, reset_global_dataframe_manager

def test_horizontal_integration():
    """Test the horizontal storage integration with full drawing generation."""
    print("=== Horizontal Storage Integration Test ===")
    
    # Reset the global DataFrame manager to start fresh
    reset_global_dataframe_manager()
    
    try:
        # Run the full drawing generation to populate the DataFrame
        print("Running full drawing generation to populate DataFrame...")
        from column_drawing.main import ColumnDrawingGenerator
        generator = ColumnDrawingGenerator()
        
        # Use the ASD CSV file for testing
        csv_file = "Rect Column Rebar Table (ASD).csv"
        output_file = "test_horizontal_integration.dxf"
        
        generator.generate_drawings(csv_file, output_file, use_zone_details=True)
        print("Drawing generation complete.")

        # Now test the DataFrame contents using the same global manager
        df_manager = get_global_dataframe_manager()
        
        if not df_manager.is_loaded():
            print("ERROR: DataFrame not loaded!")
            return
        
        # Get the DataFrame
        df = df_manager.get_dataframe()
        print(f"\nDataFrame shape: {df.shape}")
        print(f"Total columns: {len(df.columns)}")
        
        # Check that zone-specific columns were added
        zone_columns = [col for col in df.columns if col.startswith('Zone ')]
        print(f"Zone-specific columns added: {len(zone_columns)}")
        
        # Test retrieving zone-specific data
        print("\n=== Testing Zone-Specific Data Retrieval ===")
        
        # Test with a known column from the CSV
        test_column = "D7, E7, G7"
        test_floor = "PILE CAP"
        
        print(f"\nTesting column: {test_column}, floor: {test_floor}")
        
        # Check if this column exists in the DataFrame
        available_columns = df['Column Mark'].unique()
        available_floors = df['Start Floor'].unique()
        print(f"Available columns: {list(available_columns)[:5]}...")  # Show first 5
        print(f"Available floors: {list(available_floors)}")
        
        # Try to get zone-specific data for each zone
        for zone_id in ['A', 'B', 'C', 'D']:
            zone_data = df_manager.get_link_marks_for_zone(test_column, test_floor, zone_id)
            print(f"Zone {zone_id} data: {zone_data}")
        
        # Test with another column
        test_column_2 = "A3"
        print(f"\nTesting column: {test_column_2}, floor: {test_floor}")
        
        for zone_id in ['A', 'B', 'C', 'D']:
            zone_data = df_manager.get_link_marks_for_zone(test_column_2, test_floor, zone_id)
            print(f"Zone {zone_id} data: {zone_data}")
        
        # Compare with generic columns (should show combined A+C data)
        combined_data = df_manager.get_link_marks(test_column_2, test_floor)
        print(f"\nCombined data (generic columns): {combined_data}")
        
        # Show sample of the horizontal storage structure
        print("\n=== Horizontal Storage Structure Sample ===")
        
        # Find a row with data
        sample_row = None
        for _, row in df.iterrows():
            # Check if any zone-specific columns have non-empty data
            has_zone_data = False
            for col in zone_columns:
                if pd.notna(row[col]) and row[col] != '' and row[col] != 0:
                    has_zone_data = True
                    break
            
            if has_zone_data:
                sample_row = row
                break
        
        if sample_row is not None:
            print(f"Sample row: Column {sample_row['Column Mark']}, Floor {sample_row['Start Floor']}")
            print("Zone-specific data in this row:")
            
            for zone in ['A', 'B', 'C', 'D']:
                zone_data = {}
                for link_type in ['Outer Link Count', 'Outer Link Mark', 'Inner Link X Count', 'Inner Link X Mark', 'Inner Link Y Count', 'Inner Link Y Mark']:
                    col_name = f"Zone {zone} {link_type}"
                    if col_name in sample_row.index:
                        value = sample_row[col_name]
                        if pd.notna(value) and value != '' and value != 0:
                            zone_data[link_type] = value
                
                if zone_data:
                    print(f"  Zone {zone}: {zone_data}")
                else:
                    print(f"  Zone {zone}: No data")
        else:
            print("No rows found with zone-specific data")
        
        # Test the convenience method for storing individual zone data
        print("\n=== Testing Individual Zone Storage ===")
        
        # Store some test data for Zone B
        success = df_manager.store_individual_zone_marks(
            column_mark=test_column_2,
            start_floor=test_floor,
            zone_id='B',
            outer_count=8,
            outer_mark='(TEST-B)',
            inner_x_count=4,
            inner_x_mark='(TEST-B-X)',
            inner_y_count=4,
            inner_y_mark='(TEST-B-Y)'
        )
        print(f"Individual zone storage success: {success}")
        
        # Verify the storage
        zone_b_data = df_manager.get_link_marks_for_zone(test_column_2, test_floor, 'B')
        print(f"Zone B data after storage: {zone_b_data}")
        
        # Verify that other zones are not affected
        zone_a_data = df_manager.get_link_marks_for_zone(test_column_2, test_floor, 'A')
        print(f"Zone A data (should be unchanged): {zone_a_data}")
        
        print("\n=== Integration Test Complete ===")
        print("✓ Full drawing generation completed successfully")
        print("✓ Horizontal zone-specific storage working")
        print("✓ Individual zone data retrieval working")
        print("✓ Zone-specific storage methods working")
        print("✓ Backward compatibility maintained")
        print("✓ Integration with existing workflow successful")
        
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up test output file
        output_file = "test_horizontal_integration.dxf"
        if os.path.exists(output_file):
            os.remove(output_file)
            print(f"\nCleaned up test file: {output_file}")

if __name__ == "__main__":
    test_horizontal_integration()
