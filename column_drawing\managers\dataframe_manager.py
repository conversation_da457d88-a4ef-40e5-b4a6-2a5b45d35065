"""
DataFrame Manager for Centralized Link Mark Management
=====================================================

This module provides centralized management of column data using pandas DataFrames,
serving as the single source of truth for link marks across elevation and section drawings.

Key Features:
- CSV data loading and DataFrame creation
- Horizontal zone-specific storage: 24 new columns for individual zone data (6 columns × 4 zones)
- Generic columns for backward compatibility and combined data
- Zone-specific methods: update_link_marks_for_zone() and get_link_marks_for_zone()
- Centralized link mark consistency between elevation and section drawings
- Zone merging logic for combined link marks (Zone A+C)

DataFrame Structure:
- Original CSV columns: Preserved intact
- Generic link mark columns: 6 columns for backward compatibility
- Zone-specific columns: 24 columns (Zone A/B/C/D × 6 link mark types each)
- Total new columns: 30 (6 generic + 24 zone-specific)

Each row contains complete link mark information for all 4 zones of a column at a specific floor level.
"""

import logging
import pandas as pd
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path

from ..models.column_config import ColumnConfig
from ..models.zone_config import ZoneConfigSet

logger = logging.getLogger(__name__)


class DataFrameManager:
    """
    Centralized DataFrame manager for column data and link mark management.
    
    This class serves as the single source of truth for all column data,
    including generated link marks that must be consistent between elevation
    and section drawings.
    """
    
    def __init__(self):
        """Initialize the DataFrame manager."""
        self.df: Optional[pd.DataFrame] = None
        self.csv_filename: Optional[str] = None
        self._link_mark_cache: Dict[str, Dict[str, Any]] = {}
        
        logger.debug("DataFrameManager initialized")
    
    def load_csv_data(self, csv_filename: str) -> pd.DataFrame:
        """
        Load CSV data and create DataFrame with additional link mark columns.
        
        Args:
            csv_filename: Path to the CSV file
            
        Returns:
            pd.DataFrame: DataFrame with original CSV data plus new link mark columns
            
        Raises:
            FileNotFoundError: If CSV file doesn't exist
            ValueError: If CSV data is invalid
        """
        try:
            # Check if file exists
            if not Path(csv_filename).exists():
                raise FileNotFoundError(f"CSV file not found: {csv_filename}")
            
            # Load CSV data
            self.df = pd.read_csv(csv_filename)
            self.csv_filename = csv_filename
            
            # Add new columns for link mark management
            self._add_link_mark_columns()
            
            # Initialize link mark values
            self._initialize_link_mark_values()
            
            logger.info(f"Loaded CSV data from {csv_filename} with {len(self.df)} rows")
            logger.debug(f"DataFrame columns: {list(self.df.columns)}")
            
            return self.df
            
        except Exception as e:
            logger.error(f"Error loading CSV data from {csv_filename}: {e}")
            raise
    
    def _add_link_mark_columns(self) -> None:
        """Add new columns for link mark management to the DataFrame."""
        # Original generic columns for backward compatibility
        generic_columns = [
            'Outer Link Count',
            'Outer Link Mark',
            'Inner Link X Count',
            'Inner Link X Mark',
            'Inner Link Y Count',
            'Inner Link Y Mark'
        ]

        # Zone-specific columns for horizontal storage (6 columns × 4 zones = 24 columns)
        zones = ['A', 'B', 'C', 'D']
        link_types = [
            'Outer Link Count',
            'Outer Link Mark',
            'Inner Link X Count',
            'Inner Link X Mark',
            'Inner Link Y Count',
            'Inner Link Y Mark'
        ]

        zone_specific_columns = []
        for zone in zones:
            for link_type in link_types:
                zone_specific_columns.append(f"Zone {zone} {link_type}")

        # Add all columns
        all_new_columns = generic_columns + zone_specific_columns

        for col in all_new_columns:
            if col not in self.df.columns:
                self.df[col] = None

        logger.debug(f"Added {len(generic_columns)} generic link mark columns: {generic_columns}")
        logger.debug(f"Added {len(zone_specific_columns)} zone-specific link mark columns")
        logger.debug(f"Total new columns added: {len(all_new_columns)}")
    
    def _initialize_link_mark_values(self) -> None:
        """Initialize link mark columns with default values."""
        # Initialize generic count columns with 0
        generic_count_columns = ['Outer Link Count', 'Inner Link X Count', 'Inner Link Y Count']
        for col in generic_count_columns:
            self.df[col] = self.df[col].fillna(0).astype(int)

        # Initialize generic mark columns with empty strings
        generic_mark_columns = ['Outer Link Mark', 'Inner Link X Mark', 'Inner Link Y Mark']
        for col in generic_mark_columns:
            self.df[col] = self.df[col].fillna('')

        # Initialize zone-specific count columns with 0
        zones = ['A', 'B', 'C', 'D']
        count_types = ['Outer Link Count', 'Inner Link X Count', 'Inner Link Y Count']
        mark_types = ['Outer Link Mark', 'Inner Link X Mark', 'Inner Link Y Mark']

        for zone in zones:
            for count_type in count_types:
                col_name = f"Zone {zone} {count_type}"
                if col_name in self.df.columns:
                    self.df[col_name] = self.df[col_name].fillna(0).astype(int)

            for mark_type in mark_types:
                col_name = f"Zone {zone} {mark_type}"
                if col_name in self.df.columns:
                    self.df[col_name] = self.df[col_name].fillna('')

        logger.debug("Initialized generic and zone-specific link mark columns with default values")
    
    def get_column_data(self, column_mark: str, start_floor: str) -> Optional[pd.Series]:
        """
        Get column data for a specific column mark and start floor.
        
        Args:
            column_mark: Column mark identifier
            start_floor: Start floor identifier
            
        Returns:
            pd.Series: Row data for the specified column, or None if not found
        """
        if self.df is None:
            logger.warning("DataFrame not loaded. Call load_csv_data() first.")
            return None
        
        # Find matching row
        mask = (self.df['Column Mark'] == column_mark) & (self.df['Start Floor'] == start_floor)
        matching_rows = self.df[mask]
        
        if matching_rows.empty:
            logger.warning(f"No data found for column {column_mark} at floor {start_floor}")
            return None
        
        if len(matching_rows) > 1:
            logger.warning(f"Multiple rows found for column {column_mark} at floor {start_floor}. Using first match.")
        
        return matching_rows.iloc[0]
    
    def update_link_marks(self, column_mark: str, start_floor: str,
                         outer_count: int = None, outer_mark: str = None,
                         inner_x_count: int = None, inner_x_mark: str = None,
                         inner_y_count: int = None, inner_y_mark: str = None) -> bool:
        """
        Update link mark information for a specific column.

        Args:
            column_mark: Column mark identifier
            start_floor: Start floor identifier
            outer_count: Number of outer links
            outer_mark: Outer link mark identifier
            inner_x_count: Number of inner links in X direction
            inner_x_mark: Inner link X mark identifier
            inner_y_count: Number of inner links in Y direction
            inner_y_mark: Inner link Y mark identifier

        Returns:
            bool: True if update was successful, False otherwise
        """
        logger.info(f"=== DATAFRAME UPDATE === Updating link marks for column_mark={column_mark}, start_floor={start_floor}")
        logger.info(f"Parameters: outer_count={outer_count}, outer_mark={outer_mark}, inner_x_count={inner_x_count}, inner_x_mark={inner_x_mark}, inner_y_count={inner_y_count}, inner_y_mark={inner_y_mark}")

        if self.df is None:
            logger.warning("DataFrame not loaded. Call load_csv_data() first.")
            return False

        # Find matching row
        mask = (self.df['Column Mark'] == column_mark) & (self.df['Start Floor'] == start_floor)
        matching_indices = self.df[mask].index

        logger.info(f"Found {len(matching_indices)} matching rows for column_mark={column_mark}, start_floor={start_floor}")

        if len(matching_indices) == 0:
            logger.warning(f"No data found for column {column_mark} at floor {start_floor}")
            logger.info(f"Available Column Mark values: {self.df['Column Mark'].unique()}")
            logger.info(f"Available Start Floor values: {self.df['Start Floor'].unique()}")
            return False

        # Update the first matching row
        idx = matching_indices[0]
        logger.info(f"Updating row index {idx}")

        if outer_count is not None:
            self.df.at[idx, 'Outer Link Count'] = outer_count
            logger.info(f"Set Outer Link Count = {outer_count}")
        if outer_mark is not None:
            self.df.at[idx, 'Outer Link Mark'] = outer_mark
            logger.info(f"Set Outer Link Mark = {outer_mark}")
        if inner_x_count is not None:
            self.df.at[idx, 'Inner Link X Count'] = inner_x_count
            logger.info(f"Set Inner Link X Count = {inner_x_count}")
        if inner_x_mark is not None:
            self.df.at[idx, 'Inner Link X Mark'] = inner_x_mark
            logger.info(f"Set Inner Link X Mark = {inner_x_mark}")
        if inner_y_count is not None:
            self.df.at[idx, 'Inner Link Y Count'] = inner_y_count
            logger.info(f"Set Inner Link Y Count = {inner_y_count}")
        if inner_y_mark is not None:
            self.df.at[idx, 'Inner Link Y Mark'] = inner_y_mark
            logger.info(f"Set Inner Link Y Mark = {inner_y_mark}")

        logger.info(f"Successfully updated link marks for {column_mark} at {start_floor}")
        return True
    
    def get_link_marks(self, column_mark: str, start_floor: str) -> Dict[str, Any]:
        """
        Get stored link mark information for a specific column.

        Args:
            column_mark: Column mark identifier
            start_floor: Start floor identifier

        Returns:
            Dict containing link mark information
        """
        logger.debug(f"Getting link marks for column_mark={column_mark}, start_floor={start_floor}")
        logger.debug(f"Available DataFrame columns: {list(self.df.columns) if self.df is not None else 'None'}")
        if self.df is not None:
            logger.debug(f"DataFrame shape: {self.df.shape}")
            logger.debug(f"DataFrame Column Mark values: {self.df['Column Mark'].unique()}")
            logger.debug(f"DataFrame Start Floor values: {self.df['Start Floor'].unique()}")

        row_data = self.get_column_data(column_mark, start_floor)
        if row_data is None:
            logger.debug(f"No row data found for column_mark={column_mark}, start_floor={start_floor}")
            return {}

        result = {
            'outer_count': int(row_data.get('Outer Link Count', 0)),
            'outer_mark': str(row_data.get('Outer Link Mark', '')),
            'inner_x_count': int(row_data.get('Inner Link X Count', 0)),
            'inner_x_mark': str(row_data.get('Inner Link X Mark', '')),
            'inner_y_count': int(row_data.get('Inner Link Y Count', 0)),
            'inner_y_mark': str(row_data.get('Inner Link Y Mark', ''))
        }
        logger.debug(f"Returning link marks: {result}")
        return result

    def update_link_marks_for_zone(self, column_mark: str, start_floor: str, zone_id: str,
                                  outer_count: int = None, outer_mark: str = None,
                                  inner_x_count: int = None, inner_x_mark: str = None,
                                  inner_y_count: int = None, inner_y_mark: str = None) -> bool:
        """
        Update link mark information for a specific column and zone.

        Args:
            column_mark: Column mark identifier
            start_floor: Start floor identifier
            zone_id: Zone identifier (A, B, C, or D)
            outer_count: Number of outer links
            outer_mark: Outer link mark identifier
            inner_x_count: Number of inner links in X direction
            inner_x_mark: Inner link X mark identifier
            inner_y_count: Number of inner links in Y direction
            inner_y_mark: Inner link Y mark identifier

        Returns:
            bool: True if update was successful, False otherwise
        """
        logger.info(f"=== ZONE-SPECIFIC DATAFRAME UPDATE === Updating link marks for column_mark={column_mark}, start_floor={start_floor}, zone_id={zone_id}")
        logger.info(f"Parameters: outer_count={outer_count}, outer_mark={outer_mark}, inner_x_count={inner_x_count}, inner_x_mark={inner_x_mark}, inner_y_count={inner_y_count}, inner_y_mark={inner_y_mark}")

        if self.df is None:
            logger.warning("DataFrame not loaded. Call load_csv_data() first.")
            return False

        # Validate zone_id
        if zone_id not in ['A', 'B', 'C', 'D']:
            logger.error(f"Invalid zone_id: {zone_id}. Must be A, B, C, or D.")
            return False

        # Find matching row
        mask = (self.df['Column Mark'] == column_mark) & (self.df['Start Floor'] == start_floor)
        matching_indices = self.df[mask].index

        logger.info(f"Found {len(matching_indices)} matching rows for column_mark={column_mark}, start_floor={start_floor}")

        if len(matching_indices) == 0:
            logger.warning(f"No data found for column {column_mark} at floor {start_floor}")
            logger.info(f"Available Column Mark values: {self.df['Column Mark'].unique()}")
            logger.info(f"Available Start Floor values: {self.df['Start Floor'].unique()}")
            return False

        # Update the first matching row with zone-specific columns
        idx = matching_indices[0]
        logger.info(f"Updating row index {idx} for zone {zone_id}")

        if outer_count is not None:
            col_name = f"Zone {zone_id} Outer Link Count"
            self.df.at[idx, col_name] = outer_count
            logger.info(f"Set {col_name} = {outer_count}")
        if outer_mark is not None:
            col_name = f"Zone {zone_id} Outer Link Mark"
            self.df.at[idx, col_name] = outer_mark
            logger.info(f"Set {col_name} = {outer_mark}")
        if inner_x_count is not None:
            col_name = f"Zone {zone_id} Inner Link X Count"
            self.df.at[idx, col_name] = inner_x_count
            logger.info(f"Set {col_name} = {inner_x_count}")
        if inner_x_mark is not None:
            col_name = f"Zone {zone_id} Inner Link X Mark"
            self.df.at[idx, col_name] = inner_x_mark
            logger.info(f"Set {col_name} = {inner_x_mark}")
        if inner_y_count is not None:
            col_name = f"Zone {zone_id} Inner Link Y Count"
            self.df.at[idx, col_name] = inner_y_count
            logger.info(f"Set {col_name} = {inner_y_count}")
        if inner_y_mark is not None:
            col_name = f"Zone {zone_id} Inner Link Y Mark"
            self.df.at[idx, col_name] = inner_y_mark
            logger.info(f"Set {col_name} = {inner_y_mark}")

        logger.info(f"Successfully updated zone-specific link marks for {column_mark} at {start_floor}, zone {zone_id}")
        return True

    def get_link_marks_for_zone(self, column_mark: str, start_floor: str, zone_id: str) -> Dict[str, Any]:
        """
        Get stored link mark information for a specific column and zone.

        Args:
            column_mark: Column mark identifier
            start_floor: Start floor identifier
            zone_id: Zone identifier (A, B, C, or D)

        Returns:
            Dict containing zone-specific link mark information
        """
        logger.debug(f"Getting zone-specific link marks for column_mark={column_mark}, start_floor={start_floor}, zone_id={zone_id}")

        if self.df is None:
            logger.warning("DataFrame not loaded. Call load_csv_data() first.")
            return {}

        # Validate zone_id
        if zone_id not in ['A', 'B', 'C', 'D']:
            logger.error(f"Invalid zone_id: {zone_id}. Must be A, B, C, or D.")
            return {}

        row_data = self.get_column_data(column_mark, start_floor)
        if row_data is None:
            logger.debug(f"No row data found for column_mark={column_mark}, start_floor={start_floor}")
            return {}

        result = {
            'outer_count': int(row_data.get(f'Zone {zone_id} Outer Link Count', 0)),
            'outer_mark': str(row_data.get(f'Zone {zone_id} Outer Link Mark', '')),
            'inner_x_count': int(row_data.get(f'Zone {zone_id} Inner Link X Count', 0)),
            'inner_x_mark': str(row_data.get(f'Zone {zone_id} Inner Link X Mark', '')),
            'inner_y_count': int(row_data.get(f'Zone {zone_id} Inner Link Y Count', 0)),
            'inner_y_mark': str(row_data.get(f'Zone {zone_id} Inner Link Y Mark', ''))
        }
        logger.debug(f"Returning zone-specific link marks for zone {zone_id}: {result}")
        return result

    def get_combined_zone_marks(self, column_mark: str, start_floor: str,
                               zone_a_mark: str, zone_c_mark: str) -> str:
        """
        Get combined zone marks for zones A and C according to merging rules.

        Args:
            column_mark: Column mark identifier
            start_floor: Start floor identifier
            zone_a_mark: Zone A link mark
            zone_c_mark: Zone C link mark

        Returns:
            str: Combined mark following the format rules
        """
        # If marks are the same, return single mark
        if zone_a_mark == zone_c_mark:
            return zone_a_mark

        # If different, combine with "/" separator
        if zone_a_mark and zone_c_mark:
            return f"{zone_a_mark}/{zone_c_mark}"

        # Return non-empty mark if only one exists
        return zone_a_mark or zone_c_mark

    def store_zone_marks_for_merging(self, column_mark: str, start_floor: str,
                                   zone_a_outer_mark: str, zone_c_outer_mark: str,
                                   zone_a_inner_x_mark: str = None, zone_c_inner_x_mark: str = None,
                                   zone_a_inner_y_mark: str = None, zone_c_inner_y_mark: str = None,
                                   outer_count: int = 0, inner_x_count: int = 0, inner_y_count: int = 0) -> bool:
        """
        Store combined zone marks for zones A and C according to merging rules.

        This method now stores data in both zone-specific columns (for individual zone data)
        and generic columns (for combined data and backward compatibility).

        Args:
            column_mark: Column mark identifier
            start_floor: Start floor identifier
            zone_a_outer_mark: Zone A outer link mark
            zone_c_outer_mark: Zone C outer link mark
            zone_a_inner_x_mark: Zone A inner X link mark
            zone_c_inner_x_mark: Zone C inner X link mark
            zone_a_inner_y_mark: Zone A inner Y link mark
            zone_c_inner_y_mark: Zone C inner Y link mark
            outer_count: Number of outer links
            inner_x_count: Number of inner X links
            inner_y_count: Number of inner Y links

        Returns:
            bool: True if update was successful, False otherwise
        """
        try:
            logger.info(f"=== ZONE MERGING === Storing Zone A+C marks for {column_mark} at {start_floor}")

            # Store individual zone data in zone-specific columns
            zone_a_success = self.update_link_marks_for_zone(
                column_mark=column_mark,
                start_floor=start_floor,
                zone_id='A',
                outer_count=outer_count,
                outer_mark=zone_a_outer_mark,
                inner_x_count=inner_x_count,
                inner_x_mark=zone_a_inner_x_mark,
                inner_y_count=inner_y_count,
                inner_y_mark=zone_a_inner_y_mark
            )

            zone_c_success = self.update_link_marks_for_zone(
                column_mark=column_mark,
                start_floor=start_floor,
                zone_id='C',
                outer_count=outer_count,
                outer_mark=zone_c_outer_mark,
                inner_x_count=inner_x_count,
                inner_x_mark=zone_c_inner_x_mark,
                inner_y_count=inner_y_count,
                inner_y_mark=zone_c_inner_y_mark
            )

            # Combine marks according to merging rules for generic columns
            combined_outer_mark = self.get_combined_zone_marks(
                column_mark, start_floor, zone_a_outer_mark, zone_c_outer_mark
            )

            combined_inner_x_mark = self.get_combined_zone_marks(
                column_mark, start_floor, zone_a_inner_x_mark or '', zone_c_inner_x_mark or ''
            )

            combined_inner_y_mark = self.get_combined_zone_marks(
                column_mark, start_floor, zone_a_inner_y_mark or '', zone_c_inner_y_mark or ''
            )

            # Store the combined marks in the generic columns for backward compatibility
            combined_success = self.update_link_marks(
                column_mark=column_mark,
                start_floor=start_floor,
                outer_count=outer_count,
                outer_mark=combined_outer_mark,
                inner_x_count=inner_x_count,
                inner_x_mark=combined_inner_x_mark if combined_inner_x_mark else None,
                inner_y_count=inner_y_count,
                inner_y_mark=combined_inner_y_mark if combined_inner_y_mark else None
            )

            success = zone_a_success and zone_c_success and combined_success
            logger.info(f"Zone merging storage result: Zone A={zone_a_success}, Zone C={zone_c_success}, Combined={combined_success}")
            return success

        except Exception as e:
            logger.error(f"Error storing combined zone marks for {column_mark} at {start_floor}: {e}")
            return False

    def store_individual_zone_marks(self, column_mark: str, start_floor: str, zone_id: str,
                                   outer_count: int, outer_mark: str,
                                   inner_x_count: int = 0, inner_x_mark: str = None,
                                   inner_y_count: int = 0, inner_y_mark: str = None) -> bool:
        """
        Store link marks for an individual zone (B or D) that doesn't participate in merging.

        This is a convenience method for zones B and D that store their data independently
        without affecting the generic columns used for Zone A+C merging.

        Args:
            column_mark: Column mark identifier
            start_floor: Start floor identifier
            zone_id: Zone identifier (typically B or D)
            outer_count: Number of outer links
            outer_mark: Outer link mark
            inner_x_count: Number of inner X links
            inner_x_mark: Inner X link mark
            inner_y_count: Number of inner Y links
            inner_y_mark: Inner Y link mark

        Returns:
            bool: True if update was successful, False otherwise
        """
        logger.info(f"=== INDIVIDUAL ZONE STORAGE === Storing marks for {column_mark} at {start_floor}, zone {zone_id}")

        return self.update_link_marks_for_zone(
            column_mark=column_mark,
            start_floor=start_floor,
            zone_id=zone_id,
            outer_count=outer_count,
            outer_mark=outer_mark,
            inner_x_count=inner_x_count,
            inner_x_mark=inner_x_mark,
            inner_y_count=inner_y_count,
            inner_y_mark=inner_y_mark
        )

    def save_dataframe(self, output_filename: Optional[str] = None) -> bool:
        """
        Save the DataFrame back to CSV file.
        
        Args:
            output_filename: Optional output filename. If None, overwrites original file.
            
        Returns:
            bool: True if save was successful, False otherwise
        """
        if self.df is None:
            logger.warning("No DataFrame to save")
            return False
        
        filename = output_filename or self.csv_filename
        if not filename:
            logger.error("No filename specified for saving DataFrame")
            return False
        
        try:
            self.df.to_csv(filename, index=False)
            logger.info(f"Saved DataFrame to {filename}")
            return True
        except Exception as e:
            logger.error(f"Error saving DataFrame to {filename}: {e}")
            return False
    
    def get_dataframe(self) -> Optional[pd.DataFrame]:
        """
        Get the current DataFrame.
        
        Returns:
            pd.DataFrame: Current DataFrame or None if not loaded
        """
        return self.df
    
    def is_loaded(self) -> bool:
        """
        Check if DataFrame is loaded.
        
        Returns:
            bool: True if DataFrame is loaded, False otherwise
        """
        return self.df is not None


# Global instance for centralized access
_global_dataframe_manager: Optional[DataFrameManager] = None


def get_global_dataframe_manager() -> DataFrameManager:
    """
    Get the global DataFrame manager instance.
    
    Returns:
        DataFrameManager: Global DataFrame manager instance
    """
    global _global_dataframe_manager
    if _global_dataframe_manager is None:
        _global_dataframe_manager = DataFrameManager()
    return _global_dataframe_manager


def reset_global_dataframe_manager() -> None:
    """Reset the global DataFrame manager instance."""
    global _global_dataframe_manager
    _global_dataframe_manager = None
